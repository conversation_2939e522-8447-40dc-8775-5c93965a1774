<?php
/**
 * Notification Functions
 * 
 * This file contains functions related to the notification system.
 */

/**
 * Create a new notification
 * 
 * @param int $userId The user ID
 * @param string $title The notification title
 * @param string $message The notification message
 * @param string $type The notification type
 * @param string $link The notification link
 * @return int|bool The notification ID if successful, false otherwise
 */
function createNotification($userId, $title, $message, $type = 'info', $link = '') {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO notifications (user_id, title, message, type, link)
            VALUES (?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([$userId, $title, $message, $type, $link]);
        
        return $pdo->lastInsertId();
    } catch (PDOException $e) {
        error_log("Notification Creation Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Create a notification for all users with a specific role
 * 
 * @param string $role The role
 * @param string $title The notification title
 * @param string $message The notification message
 * @param string $type The notification type
 * @param string $link The notification link
 * @return bool True if successful, false otherwise
 */
function createNotificationForRole($role, $title, $message, $type = 'info', $link = '') {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT id FROM users WHERE role = ?");
        $stmt->execute([$role]);
        $users = $stmt->fetchAll();
        
        foreach ($users as $user) {
            createNotification($user['id'], $title, $message, $type, $link);
        }
        
        return true;
    } catch (PDOException $e) {
        error_log("Role Notification Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Create a notification for all users with a specific permission
 * 
 * @param string $permission The permission
 * @param string $title The notification title
 * @param string $message The notification message
 * @param string $type The notification type
 * @param string $link The notification link
 * @return bool True if successful, false otherwise
 */
function createNotificationForPermission($permission, $title, $message, $type = 'info', $link = '') {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT id, permissions FROM users");
        $stmt->execute();
        $users = $stmt->fetchAll();
        
        foreach ($users as $user) {
            $permissions = json_decode($user['permissions'], true);
            
            if (in_array($permission, $permissions) || $user['role'] === 'admin') {
                createNotification($user['id'], $title, $message, $type, $link);
            }
        }
        
        return true;
    } catch (PDOException $e) {
        error_log("Permission Notification Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Create a notification for all users in a specific hospital
 * 
 * @param int $hospitalId The hospital ID
 * @param string $title The notification title
 * @param string $message The notification message
 * @param string $type The notification type
 * @param string $link The notification link
 * @return bool True if successful, false otherwise
 */
function createNotificationForHospital($hospitalId, $title, $message, $type = 'info', $link = '') {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT id FROM users WHERE hospital_id = ?");
        $stmt->execute([$hospitalId]);
        $users = $stmt->fetchAll();
        
        foreach ($users as $user) {
            createNotification($user['id'], $title, $message, $type, $link);
        }
        
        return true;
    } catch (PDOException $e) {
        error_log("Hospital Notification Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Mark a notification as read
 * 
 * @param int $notificationId The notification ID
 * @return bool True if successful, false otherwise
 */
function markNotificationAsRead($notificationId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("UPDATE notifications SET is_read = 1 WHERE id = ?");
        return $stmt->execute([$notificationId]);
    } catch (PDOException $e) {
        error_log("Mark Notification Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Mark all notifications as read for a user
 * 
 * @param int $userId The user ID
 * @return bool True if successful, false otherwise
 */
function markAllNotificationsAsRead($userId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("UPDATE notifications SET is_read = 1 WHERE user_id = ?");
        return $stmt->execute([$userId]);
    } catch (PDOException $e) {
        error_log("Mark All Notifications Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get unread notifications for a user
 * 
 * @param int $userId The user ID
 * @param int $limit The maximum number of notifications to return
 * @return array The unread notifications
 */
function getUnreadNotifications($userId, $limit = 10) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT * FROM notifications
            WHERE user_id = ? AND is_read = 0
            ORDER BY created_at DESC
            LIMIT ?
        ");
        
        $stmt->execute([$userId, $limit]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Get Unread Notifications Error: " . $e->getMessage());
        return [];
    }
}

/**
 * Get all notifications for a user
 * 
 * @param int $userId The user ID
 * @param int $limit The maximum number of notifications to return
 * @param int $offset The offset for pagination
 * @return array The notifications
 */
function getAllNotifications($userId, $limit = 20, $offset = 0) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT * FROM notifications
            WHERE user_id = ?
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        ");
        
        $stmt->execute([$userId, $limit, $offset]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Get All Notifications Error: " . $e->getMessage());
        return [];
    }
}

/**
 * Count unread notifications for a user
 * 
 * @param int $userId The user ID
 * @return int The number of unread notifications
 */
function countUnreadNotifications($userId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM notifications
            WHERE user_id = ? AND is_read = 0
        ");
        
        $stmt->execute([$userId]);
        return (int) $stmt->fetchColumn();
    } catch (PDOException $e) {
        error_log("Count Unread Notifications Error: " . $e->getMessage());
        return 0;
    }
}

/**
 * Delete old notifications
 * 
 * @param int $days The number of days to keep notifications
 * @return bool True if successful, false otherwise
 */
function deleteOldNotifications($days = 30) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            DELETE FROM notifications
            WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
        ");
        
        return $stmt->execute([$days]);
    } catch (PDOException $e) {
        error_log("Delete Old Notifications Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Create a maintenance notification
 * 
 * @param int $deviceId The device ID
 * @param string $maintenanceType The maintenance type
 * @param string $scheduledDate The scheduled date
 * @return bool True if successful, false otherwise
 */
function createMaintenanceNotification($deviceId, $maintenanceType, $scheduledDate) {
    global $pdo;
    
    try {
        // Get device information
        $stmt = $pdo->prepare("
            SELECT d.name, d.department_id, d.hospital_id, h.name AS hospital_name
            FROM devices d
            JOIN hospitals h ON d.hospital_id = h.id
            WHERE d.id = ?
        ");
        
        $stmt->execute([$deviceId]);
        $device = $stmt->fetch();
        
        if (!$device) {
            return false;
        }
        
        // Create notification for engineers and technicians
        $title = __('maintenance_scheduled');
        $message = sprintf(
            __('maintenance_scheduled_message'),
            $maintenanceType,
            $device['name'],
            $device['hospital_name'],
            formatDate($scheduledDate)
        );
        $link = 'maintenance_schedules.php?device_id=' . $deviceId;
        
        // Notify engineers
        createNotificationForRole('engineer', $title, $message, 'info', $link);
        
        // Notify technicians
        createNotificationForRole('technician', $title, $message, 'info', $link);
        
        return true;
    } catch (PDOException $e) {
        error_log("Maintenance Notification Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Create a ticket notification
 * 
 * @param int $ticketId The ticket ID
 * @param string $action The action (created, updated, assigned, resolved, closed)
 * @param int $userId The user ID who performed the action
 * @return bool True if successful, false otherwise
 */
function createTicketNotification($ticketId, $action, $userId) {
    global $pdo;
    
    try {
        // Get ticket information
        $stmt = $pdo->prepare("
            SELECT t.*, d.name AS device_name, u.full_name AS reporter_name, a.full_name AS assignee_name
            FROM tickets t
            JOIN devices d ON t.device_id = d.id
            JOIN users u ON t.reported_by = u.id
            LEFT JOIN users a ON t.assigned_to = a.id
            WHERE t.id = ?
        ");
        
        $stmt->execute([$ticketId]);
        $ticket = $stmt->fetch();
        
        if (!$ticket) {
            return false;
        }
        
        $title = '';
        $message = '';
        $type = 'info';
        $link = 'ticket_details.php?id=' . $ticketId;
        
        // Get user who performed the action
        $stmt = $pdo->prepare("SELECT full_name FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();
        $userName = $user ? $user['full_name'] : 'System';
        
        switch ($action) {
            case 'created':
                $title = __('ticket_created');
                $message = sprintf(
                    __('ticket_created_message'),
                    $ticket['title'],
                    $ticket['device_name'],
                    $userName
                );
                break;
                
            case 'updated':
                $title = __('ticket_updated');
                $message = sprintf(
                    __('ticket_updated_message'),
                    $ticket['title'],
                    $userName
                );
                break;
                
            case 'assigned':
                $title = __('ticket_assigned');
                $message = sprintf(
                    __('ticket_assigned_message'),
                    $ticket['title'],
                    $ticket['assignee_name']
                );
                break;
                
            case 'resolved':
                $title = __('ticket_resolved');
                $message = sprintf(
                    __('ticket_resolved_message'),
                    $ticket['title'],
                    $userName
                );
                $type = 'success';
                break;
                
            case 'closed':
                $title = __('ticket_closed');
                $message = sprintf(
                    __('ticket_closed_message'),
                    $ticket['title'],
                    $userName
                );
                $type = 'success';
                break;
        }
        
        // Notify the reporter
        if ($ticket['reported_by'] != $userId) {
            createNotification($ticket['reported_by'], $title, $message, $type, $link);
        }
        
        // Notify the assignee
        if ($ticket['assigned_to'] && $ticket['assigned_to'] != $userId) {
            createNotification($ticket['assigned_to'], $title, $message, $type, $link);
        }
        
        // Notify engineers and technicians for new tickets
        if ($action === 'created') {
            createNotificationForRole('engineer', $title, $message, $type, $link);
            createNotificationForRole('technician', $title, $message, $type, $link);
        }
        
        return true;
    } catch (PDOException $e) {
        error_log("Ticket Notification Error: " . $e->getMessage());
        return false;
    }
}
