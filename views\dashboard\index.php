<?php
/**
 * Dashboard View
 *
 * This file displays the dashboard.
 */

// Set page title
$pageTitle = __('dashboard');
$pageSubtitle = __('overview_of_your_medical_devices');

// Start output buffering
ob_start();
?>

<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card card-glass border-0 mb-4">
            <div class="card-body p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="text-gradient mb-2"><?php echo __('welcome_back'); ?>, <?php echo htmlspecialchars($currentUser['full_name']); ?>!</h3>
                        <p class="text-muted mb-0"><?php echo __('dashboard_welcome_message'); ?></p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="d-flex justify-content-end">
                            <div class="text-center me-3">
                                <div class="text-primary h4 mb-1"><?php echo date('d'); ?></div>
                                <small class="text-muted"><?php echo date('M Y'); ?></small>
                            </div>
                            <div class="text-center">
                                <div class="text-primary h4 mb-1"><?php echo date('H:i'); ?></div>
                                <small class="text-muted"><?php echo date('D'); ?></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card stats-card primary h-100 fade-in-up" style="animation-delay: 0.1s;">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number animate" data-target="<?php echo $stats['devices']; ?>">0</div>
                        <div class="stats-text"><?php echo __('total_devices'); ?></div>
                        <small class="text-muted">
                            <i class="fas fa-arrow-up text-success me-1"></i>
                            <?php echo __('all_devices'); ?>
                        </small>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-microscope"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card stats-card success h-100 fade-in-up" style="animation-delay: 0.2s;">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number animate" data-target="<?php echo $stats['devices_operational']; ?>">0</div>
                        <div class="stats-text"><?php echo __('operational'); ?></div>
                        <small class="text-muted">
                            <i class="fas fa-check-circle text-success me-1"></i>
                            <?php echo round(($stats['devices'] > 0 ? ($stats['devices_operational'] / $stats['devices']) * 100 : 0), 1); ?>% <?php echo __('of_total'); ?>
                        </small>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card stats-card warning h-100 fade-in-up" style="animation-delay: 0.3s;">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number animate" data-target="<?php echo $stats['devices_maintenance']; ?>">0</div>
                        <div class="stats-text"><?php echo __('maintenance'); ?></div>
                        <small class="text-muted">
                            <i class="fas fa-tools text-warning me-1"></i>
                            <?php echo __('needs_attention'); ?>
                        </small>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-tools"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card stats-card danger h-100 fade-in-up" style="animation-delay: 0.4s;">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number animate" data-target="<?php echo $stats['devices_out_of_order']; ?>">0</div>
                        <div class="stats-text"><?php echo __('out_of_order'); ?></div>
                        <small class="text-muted">
                            <i class="fas fa-exclamation-triangle text-danger me-1"></i>
                            <?php echo __('requires_repair'); ?>
                        </small>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0"><?php echo __('recent_tickets'); ?></h5>
                <a href="<?php echo getBaseUrl(); ?>/tickets" class="btn btn-sm btn-outline-primary"><?php echo __('view_all'); ?></a>
            </div>
            <div class="card-body">
                <?php if (empty($recentTickets)): ?>
                    <p class="text-muted"><?php echo __('no_tickets'); ?></p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th><?php echo __('title'); ?></th>
                                    <th><?php echo __('device'); ?></th>
                                    <th><?php echo __('priority'); ?></th>
                                    <th><?php echo __('status'); ?></th>
                                    <th><?php echo __('actions'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentTickets as $ticket): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($ticket['title']); ?></td>
                                        <td><?php echo htmlspecialchars($ticket['device_name']); ?></td>
                                        <td>
                                            <span class="badge bg-<?php 
                                                echo $ticket['priority'] === 'low' ? 'success' : 
                                                    ($ticket['priority'] === 'medium' ? 'warning' : 
                                                        ($ticket['priority'] === 'high' ? 'danger' : 'danger')); 
                                            ?>">
                                                <?php echo __($ticket['priority']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php 
                                                echo $ticket['status'] === 'open' ? 'primary' : 
                                                    ($ticket['status'] === 'in_progress' ? 'warning' : 
                                                        ($ticket['status'] === 'resolved' ? 'success' : 'secondary')); 
                                            ?>">
                                                <?php echo __($ticket['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <a href="<?php echo getBaseUrl(); ?>/tickets/view/<?php echo $ticket['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0"><?php echo __('upcoming_maintenance'); ?></h5>
                <a href="<?php echo getBaseUrl(); ?>/maintenance" class="btn btn-sm btn-outline-primary"><?php echo __('view_all'); ?></a>
            </div>
            <div class="card-body">
                <?php if (empty($upcomingMaintenance)): ?>
                    <p class="text-muted"><?php echo __('no_upcoming_maintenance'); ?></p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th><?php echo __('title'); ?></th>
                                    <th><?php echo __('device'); ?></th>
                                    <th><?php echo __('scheduled_date'); ?></th>
                                    <th><?php echo __('actions'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($upcomingMaintenance as $schedule): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($schedule['title']); ?></td>
                                        <td><?php echo htmlspecialchars($schedule['device_name']); ?></td>
                                        <td><?php echo formatDate($schedule['scheduled_date']); ?></td>
                                        <td>
                                            <a href="<?php echo getBaseUrl(); ?>/maintenance/view_schedule/<?php echo $schedule['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if (hasPermission('manage_maintenance')): ?>
                                                <a href="<?php echo getBaseUrl(); ?>/maintenance/create_log?schedule_id=<?php echo $schedule['id']; ?>" class="btn btn-sm btn-outline-success">
                                                    <i class="fas fa-check"></i>
                                                </a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0"><?php echo __('overdue_maintenance'); ?></h5>
                <a href="<?php echo getBaseUrl(); ?>/maintenance" class="btn btn-sm btn-outline-primary"><?php echo __('view_all'); ?></a>
            </div>
            <div class="card-body">
                <?php if (empty($overdueMaintenance)): ?>
                    <p class="text-muted"><?php echo __('no_overdue_maintenance'); ?></p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th><?php echo __('title'); ?></th>
                                    <th><?php echo __('device'); ?></th>
                                    <th><?php echo __('scheduled_date'); ?></th>
                                    <th><?php echo __('actions'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($overdueMaintenance as $schedule): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($schedule['title']); ?></td>
                                        <td><?php echo htmlspecialchars($schedule['device_name']); ?></td>
                                        <td class="text-danger"><?php echo formatDate($schedule['scheduled_date']); ?></td>
                                        <td>
                                            <a href="<?php echo getBaseUrl(); ?>/maintenance/view_schedule/<?php echo $schedule['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if (hasPermission('manage_maintenance')): ?>
                                                <a href="<?php echo getBaseUrl(); ?>/maintenance/create_log?schedule_id=<?php echo $schedule['id']; ?>" class="btn btn-sm btn-outline-success">
                                                    <i class="fas fa-check"></i>
                                                </a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0"><?php echo __('ticket_status'); ?></h5>
                <a href="<?php echo getBaseUrl(); ?>/reports/ticket_resolution" class="btn btn-sm btn-outline-primary"><?php echo __('view_report'); ?></a>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div><?php echo __('open'); ?></div>
                                    <div><strong><?php echo $stats['tickets_open']; ?></strong></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div><?php echo __('in_progress'); ?></div>
                                    <div><strong><?php echo $stats['tickets_in_progress']; ?></strong></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div><?php echo __('resolved'); ?></div>
                                    <div><strong><?php echo $stats['tickets_resolved']; ?></strong></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card bg-secondary text-white">
                            <div class="card-body py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div><?php echo __('closed'); ?></div>
                                    <div><strong><?php echo $stats['tickets_closed']; ?></strong></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <a href="<?php echo getBaseUrl(); ?>/tickets/create" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        <?php echo __('create_ticket'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Add scripts for dashboard enhancements
$scripts = '
<script>
// Animate counters
function animateCounters() {
    const counters = document.querySelectorAll(".stats-number.animate");

    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute("data-target"));
        const increment = target / 100;
        let current = 0;

        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            counter.textContent = Math.floor(current);
        }, 20);
    });
}

// Initialize animations when page loads
document.addEventListener("DOMContentLoaded", function() {
    // Animate counters after a short delay
    setTimeout(animateCounters, 500);

    // Add staggered animation to cards
    const cards = document.querySelectorAll(".fade-in-up");
    cards.forEach((card, index) => {
        card.style.animationDelay = (index * 0.1) + "s";
    });
});

// Refresh data every 30 seconds
setInterval(function() {
    // You can add AJAX calls here to refresh dashboard data
    console.log("Dashboard data refresh");
}, 30000);
</script>
';

// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
