<?php
/**
 * Installation Script
 * 
 * This file creates the database tables and initial data.
 */

// Check if the script is being run directly
if (!defined('BASEPATH')) {
    define('BASEPATH', dirname(__DIR__));
}

// Create database tables
$sql = [
    // Hospitals table
    "CREATE TABLE IF NOT EXISTS hospitals (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        address TEXT,
        phone VARCHAR(50),
        email VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    // Departments table
    "CREATE TABLE IF NOT EXISTS departments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        hospital_id INT NOT NULL,
        name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (hospital_id) REFERENCES hospitals(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    // Users table
    "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        hospital_id INT,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL,
        full_name VARCHAR(255) NOT NULL,
        role ENUM('admin', 'engineer', 'technician', 'staff') NOT NULL,
        permissions JSON,
        language ENUM('en', 'ar') DEFAULT 'en',
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (hospital_id) REFERENCES hospitals(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    // Devices table
    "CREATE TABLE IF NOT EXISTS devices (
        id INT AUTO_INCREMENT PRIMARY KEY,
        hospital_id INT NOT NULL,
        department_id INT NOT NULL,
        name VARCHAR(255) NOT NULL,
        model VARCHAR(255),
        serial_number VARCHAR(255) NOT NULL,
        manufacturer VARCHAR(255),
        purchase_date DATE,
        warranty_expiry DATE,
        status ENUM('operational', 'under_maintenance', 'out_of_order', 'retired') DEFAULT 'operational',
        qr_code VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (hospital_id) REFERENCES hospitals(id) ON DELETE CASCADE,
        FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    // Maintenance schedules table
    "CREATE TABLE IF NOT EXISTS maintenance_schedules (
        id INT AUTO_INCREMENT PRIMARY KEY,
        device_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        scheduled_date DATE NOT NULL,
        frequency ENUM('once', 'daily', 'weekly', 'monthly', 'quarterly', 'yearly') DEFAULT 'once',
        status ENUM('scheduled', 'completed', 'overdue', 'cancelled') DEFAULT 'scheduled',
        created_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    // Maintenance logs table
    "CREATE TABLE IF NOT EXISTS maintenance_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        maintenance_schedule_id INT,
        device_id INT NOT NULL,
        performed_by INT NOT NULL,
        performed_date DATE NOT NULL,
        notes TEXT,
        status ENUM('completed', 'incomplete') DEFAULT 'completed',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (maintenance_schedule_id) REFERENCES maintenance_schedules(id) ON DELETE SET NULL,
        FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
        FOREIGN KEY (performed_by) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    // Tickets table
    "CREATE TABLE IF NOT EXISTS tickets (
        id INT AUTO_INCREMENT PRIMARY KEY,
        device_id INT NOT NULL,
        reported_by INT NOT NULL,
        assigned_to INT,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        priority ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
        status ENUM('open', 'in_progress', 'resolved', 'closed') DEFAULT 'open',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
        FOREIGN KEY (reported_by) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    // Ticket updates table
    "CREATE TABLE IF NOT EXISTS ticket_updates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        ticket_id INT NOT NULL,
        user_id INT NOT NULL,
        comment TEXT,
        status_change VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (ticket_id) REFERENCES tickets(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    // Notifications table
    "CREATE TABLE IF NOT EXISTS notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT,
        type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
        is_read BOOLEAN DEFAULT FALSE,
        link VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    // Activity logs table
    "CREATE TABLE IF NOT EXISTS activity_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        action VARCHAR(255) NOT NULL,
        details TEXT,
        ip_address VARCHAR(45),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    // Password resets table
    "CREATE TABLE IF NOT EXISTS password_resets (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        token VARCHAR(255) NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    // Remember tokens table
    "CREATE TABLE IF NOT EXISTS remember_tokens (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        token VARCHAR(255) NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
];

// Execute SQL statements
foreach ($sql as $query) {
    try {
        $pdo->exec($query);
    } catch (PDOException $e) {
        die("Error creating tables: " . $e->getMessage());
    }
}

// Create default hospital
try {
    $stmt = $pdo->prepare("
        INSERT INTO hospitals (name, address, phone, email)
        VALUES (?, ?, ?, ?)
    ");
    
    $stmt->execute([
        'Main Hospital',
        '123 Main Street, City',
        '+**********',
        '<EMAIL>'
    ]);
    
    $hospitalId = $pdo->lastInsertId();
} catch (PDOException $e) {
    die("Error creating default hospital: " . $e->getMessage());
}

// Create default departments
try {
    $departments = [
        ['Cardiology', 'Heart and cardiovascular system'],
        ['Neurology', 'Brain and nervous system'],
        ['Radiology', 'Medical imaging'],
        ['Laboratory', 'Medical testing'],
        ['Emergency', 'Emergency care']
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO departments (hospital_id, name, description)
        VALUES (?, ?, ?)
    ");
    
    foreach ($departments as $department) {
        $stmt->execute([
            $hospitalId,
            $department[0],
            $department[1]
        ]);
    }
} catch (PDOException $e) {
    die("Error creating default departments: " . $e->getMessage());
}

// Create default admin user
try {
    $password = password_hash('admin123', PASSWORD_DEFAULT);
    $permissions = json_encode([
        'view_dashboard',
        'manage_users',
        'manage_hospitals',
        'manage_departments',
        'manage_devices',
        'manage_maintenance',
        'manage_tickets',
        'view_reports',
        'export_data'
    ]);
    
    $stmt = $pdo->prepare("
        INSERT INTO users (
            hospital_id, username, password, email, full_name,
            role, permissions, language
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?, ?
        )
    ");
    
    $stmt->execute([
        $hospitalId,
        'admin',
        $password,
        '<EMAIL>',
        'System Administrator',
        'admin',
        $permissions,
        'en'
    ]);
} catch (PDOException $e) {
    die("Error creating default admin user: " . $e->getMessage());
}

// Create uploads directory if it doesn't exist
$uploadsDir = BASEPATH . '/uploads/qrcodes';
if (!is_dir($uploadsDir)) {
    mkdir($uploadsDir, 0755, true);
}

// Create success message
echo '<div style="max-width: 600px; margin: 50px auto; padding: 20px; background-color: #f8f9fa; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">';
echo '<h2 style="color: #28a745;">Installation Successful!</h2>';
echo '<p>The Medical Device Management System has been successfully installed.</p>';
echo '<p>Default admin credentials:</p>';
echo '<ul>';
echo '<li><strong>Username:</strong> admin</li>';
echo '<li><strong>Password:</strong> admin123</li>';
echo '</ul>';
echo '<p><strong>Important:</strong> Please change the default password after logging in.</p>';
echo '<p><a href="../login.php" style="display: inline-block; padding: 10px 15px; background-color: #007bff; color: #fff; text-decoration: none; border-radius: 3px;">Go to Login Page</a></p>';
echo '</div>';

// Log installation
try {
    $stmt = $pdo->prepare("
        INSERT INTO activity_logs (action, details, ip_address)
        VALUES (?, ?, ?)
    ");
    
    $stmt->execute([
        'system_install',
        'System installed successfully',
        $_SERVER['REMOTE_ADDR'] ?? 'Unknown'
    ]);
} catch (PDOException $e) {
    // Ignore logging errors
}

// Redirect to login page after 5 seconds
header("Refresh: 5; URL=../login.php");
