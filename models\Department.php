<?php
/**
 * Department Model
 * 
 * This class handles department-related database operations.
 */
class Department {
    private $pdo;
    
    /**
     * Constructor
     * 
     * @param PDO $pdo The PDO instance
     */
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * Get all departments
     * 
     * @param int $hospitalId Optional hospital ID to filter departments
     * @return array The departments
     */
    public function getAll($hospitalId = null) {
        try {
            $sql = "
                SELECT d.*, h.name AS hospital_name
                FROM departments d
                JOIN hospitals h ON d.hospital_id = h.id
            ";
            $params = [];
            
            if ($hospitalId) {
                $sql .= " WHERE d.hospital_id = ?";
                $params[] = $hospitalId;
            }
            
            $sql .= " ORDER BY h.name, d.name";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get All Departments Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get a department by ID
     * 
     * @param int $id The department ID
     * @return array|bool The department or false if not found
     */
    public function getById($id) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT d.*, h.name AS hospital_name
                FROM departments d
                JOIN hospitals h ON d.hospital_id = h.id
                WHERE d.id = ?
            ");
            
            $stmt->execute([$id]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get Department By ID Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create a new department
     * 
     * @param array $data The department data
     * @return int|bool The department ID if successful, false otherwise
     */
    public function create($data) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO departments (hospital_id, name, description)
                VALUES (?, ?, ?)
            ");
            
            $stmt->execute([
                $data['hospital_id'],
                $data['name'],
                $data['description']
            ]);
            
            return $this->pdo->lastInsertId();
        } catch (PDOException $e) {
            error_log("Create Department Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update a department
     * 
     * @param int $id The department ID
     * @param array $data The department data
     * @return bool True if successful, false otherwise
     */
    public function update($id, $data) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE departments SET
                    hospital_id = ?,
                    name = ?,
                    description = ?,
                    updated_at = NOW()
                WHERE id = ?
            ");
            
            return $stmt->execute([
                $data['hospital_id'],
                $data['name'],
                $data['description'],
                $id
            ]);
        } catch (PDOException $e) {
            error_log("Update Department Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete a department
     * 
     * @param int $id The department ID
     * @return bool True if successful, false otherwise
     */
    public function delete($id) {
        try {
            // Check if there are any devices in this department
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM devices WHERE department_id = ?");
            $stmt->execute([$id]);
            $deviceCount = $stmt->fetchColumn();
            
            if ($deviceCount > 0) {
                // Cannot delete a department with devices
                return false;
            }
            
            $stmt = $this->pdo->prepare("DELETE FROM departments WHERE id = ?");
            return $stmt->execute([$id]);
        } catch (PDOException $e) {
            error_log("Delete Department Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get departments by hospital
     * 
     * @param int $hospitalId The hospital ID
     * @return array The departments
     */
    public function getByHospital($hospitalId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT * FROM departments
                WHERE hospital_id = ?
                ORDER BY name
            ");
            
            $stmt->execute([$hospitalId]);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get Departments By Hospital Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get department statistics
     * 
     * @param int $id The department ID
     * @return array The statistics
     */
    public function getStatistics($id) {
        try {
            $stats = [
                'devices' => 0,
                'devices_operational' => 0,
                'devices_maintenance' => 0,
                'devices_out_of_order' => 0,
                'devices_retired' => 0,
                'maintenance_scheduled' => 0,
                'maintenance_overdue' => 0,
                'tickets_open' => 0,
                'tickets_in_progress' => 0,
                'tickets_resolved' => 0,
                'tickets_closed' => 0
            ];
            
            // Count devices
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM devices WHERE department_id = ?");
            $stmt->execute([$id]);
            $stats['devices'] = (int) $stmt->fetchColumn();
            
            // Count devices by status
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM devices WHERE department_id = ? AND status = 'operational'");
            $stmt->execute([$id]);
            $stats['devices_operational'] = (int) $stmt->fetchColumn();
            
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM devices WHERE department_id = ? AND status = 'under_maintenance'");
            $stmt->execute([$id]);
            $stats['devices_maintenance'] = (int) $stmt->fetchColumn();
            
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM devices WHERE department_id = ? AND status = 'out_of_order'");
            $stmt->execute([$id]);
            $stats['devices_out_of_order'] = (int) $stmt->fetchColumn();
            
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM devices WHERE department_id = ? AND status = 'retired'");
            $stmt->execute([$id]);
            $stats['devices_retired'] = (int) $stmt->fetchColumn();
            
            // Count maintenance schedules
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) FROM maintenance_schedules ms
                JOIN devices d ON ms.device_id = d.id
                WHERE d.department_id = ? AND ms.status = 'scheduled'
            ");
            $stmt->execute([$id]);
            $stats['maintenance_scheduled'] = (int) $stmt->fetchColumn();
            
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) FROM maintenance_schedules ms
                JOIN devices d ON ms.device_id = d.id
                WHERE d.department_id = ? AND ms.status = 'overdue'
            ");
            $stmt->execute([$id]);
            $stats['maintenance_overdue'] = (int) $stmt->fetchColumn();
            
            // Count tickets
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) FROM tickets t
                JOIN devices d ON t.device_id = d.id
                WHERE d.department_id = ? AND t.status = 'open'
            ");
            $stmt->execute([$id]);
            $stats['tickets_open'] = (int) $stmt->fetchColumn();
            
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) FROM tickets t
                JOIN devices d ON t.device_id = d.id
                WHERE d.department_id = ? AND t.status = 'in_progress'
            ");
            $stmt->execute([$id]);
            $stats['tickets_in_progress'] = (int) $stmt->fetchColumn();
            
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) FROM tickets t
                JOIN devices d ON t.device_id = d.id
                WHERE d.department_id = ? AND t.status = 'resolved'
            ");
            $stmt->execute([$id]);
            $stats['tickets_resolved'] = (int) $stmt->fetchColumn();
            
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) FROM tickets t
                JOIN devices d ON t.device_id = d.id
                WHERE d.department_id = ? AND t.status = 'closed'
            ");
            $stmt->execute([$id]);
            $stats['tickets_closed'] = (int) $stmt->fetchColumn();
            
            return $stats;
        } catch (PDOException $e) {
            error_log("Get Department Statistics Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Count departments
     *
     * @param int $hospitalId Optional hospital ID to filter departments
     * @return int The number of departments
     */
    public function count($hospitalId = null) {
        try {
            $sql = "SELECT COUNT(*) FROM departments";
            $params = [];

            if ($hospitalId) {
                $sql .= " WHERE hospital_id = ?";
                $params[] = $hospitalId;
            }

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);

            return (int) $stmt->fetchColumn();
        } catch (PDOException $e) {
            error_log("Count Departments Error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Count departments by hospital
     *
     * @param int $hospitalId The hospital ID
     * @return int The number of departments
     */
    public function countByHospital($hospitalId) {
        return $this->count($hospitalId);
    }
}
