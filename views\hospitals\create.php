<?php
/**
 * Create Hospital View
 *
 * This file displays the form to create a new hospital.
 */

// Set page title
$pageTitle = __('add_hospital');
$pageSubtitle = __('create_new_hospital_facility');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 text-gradient mb-1"><?php echo __('add_hospital'); ?></h1>
        <p class="text-muted mb-0"><?php echo __('create_new_hospital_facility'); ?></p>
    </div>

    <a href="<?php echo getBaseUrl(); ?>/hospitals" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
    </a>
</div>

<div class="row">
    <div class="col-lg-8 col-md-10 mx-auto">
        <div class="card fade-in-up">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-hospital me-2"></i><?php echo __('hospital_information'); ?>
                </h5>
            </div>
            <div class="card-body">
        <form action="<?php echo getBaseUrl(); ?>/hospitals/store" method="post" id="hospitalForm">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="name" class="form-label"><?php echo __('name'); ?> <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required>
                        <div class="invalid-feedback" id="name-error"></div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="phone" class="form-label"><?php echo __('phone'); ?> <span class="text-danger">*</span></label>
                        <input type="tel" class="form-control" id="phone" name="phone" required>
                        <div class="invalid-feedback" id="phone-error"></div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="email" class="form-label"><?php echo __('email'); ?> <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" id="email" name="email" required>
                        <div class="invalid-feedback" id="email-error"></div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="website" class="form-label"><?php echo __('website'); ?></label>
                        <input type="url" class="form-control" id="website" name="website">
                        <div class="invalid-feedback" id="website-error"></div>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="address" class="form-label"><?php echo __('address'); ?> <span class="text-danger">*</span></label>
                <textarea class="form-control" id="address" name="address" rows="2" required></textarea>
                <div class="invalid-feedback" id="address-error"></div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="city" class="form-label"><?php echo __('city'); ?> <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="city" name="city" required>
                        <div class="invalid-feedback" id="city-error"></div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="country" class="form-label"><?php echo __('country'); ?> <span class="text-danger">*</span></label>
                        <select class="form-select" id="country" name="country" required>
                            <option value=""><?php echo __('select_country'); ?></option>
                            <?php foreach ($countries as $code => $country): ?>
                                <option value="<?php echo $country; ?>"><?php echo $country; ?></option>
                            <?php endforeach; ?>
                        </select>
                        <div class="invalid-feedback" id="country-error"></div>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="notes" class="form-label"><?php echo __('notes'); ?></label>
                <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
            </div>
            
            <div class="d-flex justify-content-between align-items-center pt-3 border-top">
                <a href="<?php echo getBaseUrl(); ?>/hospitals" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                </a>
                <div class="d-flex gap-2">
                    <button type="reset" class="btn btn-outline-secondary">
                        <i class="fas fa-undo me-2"></i><?php echo __('reset'); ?>
                    </button>
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <span class="btn-text">
                            <i class="fas fa-save me-2"></i><?php echo __('create_hospital'); ?>
                        </span>
                        <span class="loading-spinner d-none"></span>
                    </button>
                </div>
            </div>
        </form>
    </div>
        </div>
    </div>
</div>

<?php
// Add scripts
$scripts = '
<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize form validation
        $("#hospitalForm").validate({
            rules: {
                name: {
                    required: true,
                    minlength: 2,
                    maxlength: 100
                },
                address: {
                    required: true,
                    minlength: 5,
                    maxlength: 255
                },
                city: {
                    required: true,
                    minlength: 2,
                    maxlength: 100
                },
                country: {
                    required: true
                },
                phone: {
                    required: true,
                    minlength: 5,
                    maxlength: 20
                },
                email: {
                    required: true,
                    email: true
                },
                website: {
                    url: true
                }
            },
            messages: {
                name: {
                    required: "' . __('field_required') . '",
                    minlength: "' . __('min_length', ['length' => 2]) . '",
                    maxlength: "' . __('max_length', ['length' => 100]) . '"
                },
                address: {
                    required: "' . __('field_required') . '",
                    minlength: "' . __('min_length', ['length' => 5]) . '",
                    maxlength: "' . __('max_length', ['length' => 255]) . '"
                },
                city: {
                    required: "' . __('field_required') . '",
                    minlength: "' . __('min_length', ['length' => 2]) . '",
                    maxlength: "' . __('max_length', ['length' => 100]) . '"
                },
                country: {
                    required: "' . __('field_required') . '"
                },
                phone: {
                    required: "' . __('field_required') . '",
                    minlength: "' . __('min_length', ['length' => 5]) . '",
                    maxlength: "' . __('max_length', ['length' => 20]) . '"
                },
                email: {
                    required: "' . __('field_required') . '",
                    email: "' . __('invalid_email') . '"
                },
                website: {
                    url: "' . __('invalid_url') . '"
                }
            },
            errorElement: "div",
            errorPlacement: function(error, element) {
                error.addClass("invalid-feedback");
                error.insertAfter(element);
            },
            highlight: function(element, errorClass, validClass) {
                $(element).addClass("is-invalid").removeClass("is-valid");
            },
            unhighlight: function(element, errorClass, validClass) {
                $(element).removeClass("is-invalid").addClass("is-valid");
            },
            submitHandler: function(form) {
                // Show loading state
                const submitBtn = document.getElementById("submitBtn");
                const btnText = submitBtn.querySelector(".btn-text");
                const spinner = submitBtn.querySelector(".loading-spinner");

                btnText.classList.add("d-none");
                spinner.classList.remove("d-none");
                submitBtn.disabled = true;

                form.submit();
            }
        });

        // Add form animations
        $(".form-control, .form-select").on("focus", function() {
            $(this).parent().addClass("focused");
        }).on("blur", function() {
            $(this).parent().removeClass("focused");
        });

        // Auto-save draft functionality (optional)
        let formData = {};
        $("input, select, textarea").on("change", function() {
            formData[this.name] = this.value;
            localStorage.setItem("hospital_draft", JSON.stringify(formData));
        });

        // Load draft on page load
        const draft = localStorage.getItem("hospital_draft");
        if (draft) {
            const data = JSON.parse(draft);
            Object.keys(data).forEach(key => {
                const element = document.querySelector(`[name="${key}"]`);
                if (element && element.value === "") {
                    element.value = data[key];
                }
            });
        }
    });
</script>
';

// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
