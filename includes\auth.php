<?php
/**
 * Authentication Functions
 * 
 * This file contains functions related to user authentication.
 */

/**
 * Authenticate a user
 * 
 * @param string $username The username
 * @param string $password The password
 * @return bool True if authentication is successful, false otherwise
 */
function authenticate($username, $password) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
    $stmt->execute([$username]);
    $user = $stmt->fetch();
    
    if ($user && password_verify($password, $user['password'])) {
        // Decode permissions if they exist
        $permissions = [];
        if (!empty($user['permissions'])) {
            $decodedPermissions = json_decode($user['permissions'], true);
            $permissions = is_array($decodedPermissions) ? $decodedPermissions : [];
        }

        // If no permissions are set, get default permissions for the role
        if (empty($permissions)) {
            $permissions = getDefaultPermissions($user['role']);
        }

        // Set session variables
        $_SESSION['user'] = [
            'id' => $user['id'],
            'username' => $user['username'],
            'full_name' => $user['full_name'],
            'email' => $user['email'],
            'role' => $user['role'],
            'permissions' => $permissions,
            'hospital_id' => $user['hospital_id'],
            'language' => $user['language']
        ];
        
        // Set the user's language preference
        setLanguage($user['language']);
        
        // Update last login time
        $stmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
        $stmt->execute([$user['id']]);
        
        // Log the login action
        logAction('login', 'User logged in');
        
        return true;
    }
    
    return false;
}

/**
 * Log out the current user
 * 
 * @return void
 */
function logout() {
    // Log the logout action
    if (isset($_SESSION['user'])) {
        logAction('logout', 'User logged out');
    }
    
    // Unset all session variables
    $_SESSION = [];
    
    // Destroy the session
    session_destroy();
}

/**
 * Register a new user
 * 
 * @param array $userData The user data
 * @return int|bool The user ID if registration is successful, false otherwise
 */
function registerUser($userData) {
    global $pdo;
    
    // Hash the password
    $hashedPassword = password_hash($userData['password'], PASSWORD_DEFAULT);
    
    // Set default permissions based on role
    $permissions = getDefaultPermissions($userData['role']);
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO users (
                username, password, email, full_name, role, permissions, hospital_id, language
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?
            )
        ");
        
        $stmt->execute([
            $userData['username'],
            $hashedPassword,
            $userData['email'],
            $userData['full_name'],
            $userData['role'],
            json_encode($permissions),
            $userData['hospital_id'],
            $userData['language'] ?? 'en'
        ]);
        
        $userId = $pdo->lastInsertId();
        
        // Log the action
        logAction('register', 'New user registered: ' . $userData['username']);
        
        return $userId;
    } catch (PDOException $e) {
        error_log("User Registration Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Update a user's profile
 * 
 * @param int $userId The user ID
 * @param array $userData The user data
 * @return bool True if update is successful, false otherwise
 */
function updateUserProfile($userId, $userData) {
    global $pdo;
    
    try {
        $sql = "
            UPDATE users SET
                email = ?,
                full_name = ?,
                language = ?
        ";
        
        $params = [
            $userData['email'],
            $userData['full_name'],
            $userData['language'] ?? 'en'
        ];
        
        // If password is being updated
        if (!empty($userData['password'])) {
            $hashedPassword = password_hash($userData['password'], PASSWORD_DEFAULT);
            $sql .= ", password = ?";
            $params[] = $hashedPassword;
        }
        
        // If role is being updated (admin only)
        if (isset($userData['role']) && hasPermission('manage_users')) {
            $sql .= ", role = ?";
            $params[] = $userData['role'];
        }
        
        // If permissions are being updated (admin only)
        if (isset($userData['permissions']) && hasPermission('manage_users')) {
            $sql .= ", permissions = ?";
            $params[] = json_encode($userData['permissions']);
        }
        
        // If hospital is being updated (admin only)
        if (isset($userData['hospital_id']) && hasPermission('manage_users')) {
            $sql .= ", hospital_id = ?";
            $params[] = $userData['hospital_id'];
        }
        
        $sql .= " WHERE id = ?";
        $params[] = $userId;
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute($params);
        
        // Update session if the current user is being updated
        if ($result && isset($_SESSION['user']) && $_SESSION['user']['id'] == $userId) {
            $_SESSION['user']['email'] = $userData['email'];
            $_SESSION['user']['full_name'] = $userData['full_name'];
            
            if (isset($userData['language'])) {
                $_SESSION['user']['language'] = $userData['language'];
                setLanguage($userData['language']);
            }
            
            if (isset($userData['role'])) {
                $_SESSION['user']['role'] = $userData['role'];
            }
            
            if (isset($userData['permissions'])) {
                $_SESSION['user']['permissions'] = json_encode($userData['permissions']);
            }
            
            if (isset($userData['hospital_id'])) {
                $_SESSION['user']['hospital_id'] = $userData['hospital_id'];
            }
        }
        
        // Log the action
        logAction('update_profile', 'User profile updated: ' . $userId);
        
        return $result;
    } catch (PDOException $e) {
        error_log("User Profile Update Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Change a user's password
 * 
 * @param int $userId The user ID
 * @param string $currentPassword The current password
 * @param string $newPassword The new password
 * @return bool True if change is successful, false otherwise
 */
function changePassword($userId, $currentPassword, $newPassword) {
    global $pdo;
    
    // Verify current password
    $stmt = $pdo->prepare("SELECT password FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch();
    
    if (!$user || !password_verify($currentPassword, $user['password'])) {
        return false;
    }
    
    // Hash the new password
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    
    try {
        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
        $result = $stmt->execute([$hashedPassword, $userId]);
        
        // Log the action
        if ($result) {
            logAction('change_password', 'User password changed: ' . $userId);
        }
        
        return $result;
    } catch (PDOException $e) {
        error_log("Password Change Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get default permissions for a role
 * 
 * @param string $role The role
 * @return array The default permissions
 */
function getDefaultPermissions($role) {
    switch ($role) {
        case 'admin':
            return [
                'view_dashboard',
                'manage_users',
                'manage_hospitals',
                'manage_departments',
                'manage_devices',
                'manage_maintenance',
                'manage_tickets',
                'view_reports',
                'export_data'
            ];
        
        case 'engineer':
            return [
                'view_dashboard',
                'view_hospitals',
                'view_departments',
                'manage_devices',
                'manage_maintenance',
                'manage_tickets',
                'view_reports'
            ];
        
        case 'technician':
            return [
                'view_dashboard',
                'view_hospitals',
                'view_departments',
                'view_devices',
                'manage_maintenance',
                'manage_tickets'
            ];
        
        case 'staff':
            return [
                'view_dashboard',
                'view_devices',
                'create_tickets',
                'view_tickets'
            ];
        
        default:
            return ['view_dashboard'];
    }
}

/**
 * Require authentication
 * 
 * Redirects to the login page if the user is not logged in
 * 
 * @return void
 */
function requireLogin() {
    if (!isLoggedIn()) {
        setFlashMessage('error', __('access_denied'));
        redirect('login.php');
    }
}

/**
 * Require a specific permission
 * 
 * Redirects to the dashboard if the user doesn't have the required permission
 * 
 * @param string $permission The required permission
 * @return void
 */
function requirePermission($permission) {
    requireLogin();
    
    if (!hasPermission($permission)) {
        setFlashMessage('error', __('access_denied'));
        redirect('dashboard.php');
    }
}

/**
 * Generate a password reset token
 * 
 * @param string $email The user's email
 * @return string|bool The reset token if successful, false otherwise
 */
function generatePasswordResetToken($email) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute([$email]);
    $user = $stmt->fetch();
    
    if (!$user) {
        return false;
    }
    
    $token = bin2hex(random_bytes(32));
    $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO password_resets (user_id, token, expires_at)
            VALUES (?, ?, ?)
        ");
        
        $stmt->execute([$user['id'], $token, $expires]);
        
        return $token;
    } catch (PDOException $e) {
        error_log("Password Reset Token Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Verify a password reset token
 * 
 * @param string $token The reset token
 * @return int|bool The user ID if the token is valid, false otherwise
 */
function verifyPasswordResetToken($token) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT user_id FROM password_resets
            WHERE token = ? AND expires_at > NOW()
            ORDER BY created_at DESC
            LIMIT 1
        ");
        
        $stmt->execute([$token]);
        $reset = $stmt->fetch();
        
        return $reset ? $reset['user_id'] : false;
    } catch (PDOException $e) {
        error_log("Password Reset Verification Error: " . $e->getMessage());
        return false;
    }
}
