<?php
/**
 * Users Controller
 * 
 * This file handles user-related operations.
 */

// Check if the user is logged in and has permission to manage users
requirePermission('manage_users');

// Get the current user
$currentUser = getCurrentUser();
$hospitalId = $currentUser['hospital_id'];

// Handle actions
$action = isset($url[1]) ? $url[1] : 'index';
$userId = isset($url[2]) ? (int)$url[2] : 0;

switch ($action) {
    case 'index':
        // Get all users
        if ($hospitalId && $currentUser['role'] !== 'admin') {
            // Non-admin users can only see users from their hospital
            $users = $userModel->getAll($hospitalId);
        } else {
            // Admin can see all users
            $users = $userModel->getAll();
        }
        
        // Get hospitals for the dropdown
        $hospitals = $hospitalModel->getAll();
        
        // Include the users index view
        include 'views/users/index.php';
        break;
        
    case 'create':
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $userData = [
                'username' => $_POST['username'] ?? '',
                'password' => $_POST['password'] ?? '',
                'email' => $_POST['email'] ?? '',
                'full_name' => $_POST['full_name'] ?? '',
                'role' => $_POST['role'] ?? 'staff',
                'hospital_id' => $_POST['hospital_id'] ?? $hospitalId,
                'language' => $_POST['language'] ?? 'en'
            ];
            
            // Validate data
            $errors = [];
            
            if (empty($userData['username'])) {
                $errors['username'] = __('required_field');
            } elseif ($userModel->getByUsername($userData['username'])) {
                $errors['username'] = __('username_taken');
            }
            
            if (empty($userData['password'])) {
                $errors['password'] = __('required_field');
            } elseif (strlen($userData['password']) < 6) {
                $errors['password'] = __('password_too_short');
            }
            
            if (empty($userData['email'])) {
                $errors['email'] = __('required_field');
            } elseif (!filter_var($userData['email'], FILTER_VALIDATE_EMAIL)) {
                $errors['email'] = __('invalid_email');
            } elseif ($userModel->getByEmail($userData['email'])) {
                $errors['email'] = __('email_taken');
            }
            
            if (empty($userData['full_name'])) {
                $errors['full_name'] = __('required_field');
            }
            
            // If no errors, create the user
            if (empty($errors)) {
                $userId = $userModel->create($userData);
                
                if ($userId) {
                    // Log the action
                    logAction('create_user', 'Created user: ' . $userData['username']);
                    
                    // Set flash message
                    setFlashMessage('success', __('created_successfully', [__('user')]));
                    
                    // Redirect to users index
                    redirect('users');
                } else {
                    $errors['general'] = __('create_failed');
                }
            }
        } else {
            // Initialize empty user data
            $userData = [
                'username' => '',
                'password' => '',
                'email' => '',
                'full_name' => '',
                'role' => 'staff',
                'hospital_id' => $hospitalId,
                'language' => 'en'
            ];
            
            $errors = [];
        }
        
        // Get hospitals for the dropdown
        $hospitals = $hospitalModel->getAll();
        
        // Include the users create view
        include 'views/users/create.php';
        break;
        
    case 'edit':
        // Get the user
        $user = $userModel->getById($userId);
        
        // Check if the user exists
        if (!$user) {
            setFlashMessage('error', __('not_found'));
            redirect('users');
        }
        
        // Check if the current user has permission to edit this user
        if ($hospitalId && $user['hospital_id'] != $hospitalId && $currentUser['role'] !== 'admin') {
            setFlashMessage('error', __('access_denied'));
            redirect('users');
        }
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $userData = [
                'email' => $_POST['email'] ?? $user['email'],
                'full_name' => $_POST['full_name'] ?? $user['full_name'],
                'role' => $_POST['role'] ?? $user['role'],
                'hospital_id' => $_POST['hospital_id'] ?? $user['hospital_id'],
                'language' => $_POST['language'] ?? $user['language']
            ];
            
            // Only update password if provided
            if (!empty($_POST['password'])) {
                $userData['password'] = $_POST['password'];
            }
            
            // Validate data
            $errors = [];
            
            if (empty($userData['email'])) {
                $errors['email'] = __('required_field');
            } elseif (!filter_var($userData['email'], FILTER_VALIDATE_EMAIL)) {
                $errors['email'] = __('invalid_email');
            } elseif ($userData['email'] !== $user['email'] && $userModel->getByEmail($userData['email'])) {
                $errors['email'] = __('email_taken');
            }
            
            if (empty($userData['full_name'])) {
                $errors['full_name'] = __('required_field');
            }
            
            if (!empty($userData['password']) && strlen($userData['password']) < 6) {
                $errors['password'] = __('password_too_short');
            }
            
            // If no errors, update the user
            if (empty($errors)) {
                $result = $userModel->update($userId, $userData);
                
                if ($result) {
                    // Log the action
                    logAction('update_user', 'Updated user: ' . $user['username']);
                    
                    // Set flash message
                    setFlashMessage('success', __('updated_successfully', [__('user')]));
                    
                    // Redirect to users index
                    redirect('users');
                } else {
                    $errors['general'] = __('update_failed');
                }
            }
        } else {
            $userData = $user;
            $errors = [];
        }
        
        // Get hospitals for the dropdown
        $hospitals = $hospitalModel->getAll();
        
        // Include the users edit view
        include 'views/users/edit.php';
        break;
        
    case 'delete':
        // Get the user
        $user = $userModel->getById($userId);
        
        // Check if the user exists
        if (!$user) {
            setFlashMessage('error', __('not_found'));
            redirect('users');
        }
        
        // Check if the current user has permission to delete this user
        if ($hospitalId && $user['hospital_id'] != $hospitalId && $currentUser['role'] !== 'admin') {
            setFlashMessage('error', __('access_denied'));
            redirect('users');
        }
        
        // Check if the user is trying to delete themselves
        if ($user['id'] === $currentUser['id']) {
            setFlashMessage('error', __('cannot_delete_self'));
            redirect('users');
        }
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm']) && $_POST['confirm'] === 'yes') {
            $result = $userModel->delete($userId);
            
            if ($result) {
                // Log the action
                logAction('delete_user', 'Deleted user: ' . $user['username']);
                
                // Set flash message
                setFlashMessage('success', __('deleted_successfully', [__('user')]));
            } else {
                setFlashMessage('error', __('delete_failed'));
            }
            
            // Redirect to users index
            redirect('users');
        }
        
        // Include the users delete view
        include 'views/users/delete.php';
        break;
        
    case 'view':
        // Get the user
        $user = $userModel->getById($userId);
        
        // Check if the user exists
        if (!$user) {
            setFlashMessage('error', __('not_found'));
            redirect('users');
        }
        
        // Check if the current user has permission to view this user
        if ($hospitalId && $user['hospital_id'] != $hospitalId && $currentUser['role'] !== 'admin') {
            setFlashMessage('error', __('access_denied'));
            redirect('users');
        }
        
        // Include the users view
        include 'views/users/view.php';
        break;
        
    default:
        // 404 Not Found
        header("HTTP/1.0 404 Not Found");
        include 'views/404.php';
        break;
}
